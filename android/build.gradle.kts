buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://developer.huawei.com/repo/")
        }
    }
    dependencies {
        // AGC 插件
        classpath("com.huawei.agconnect:agcp:1.9.1.300")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // 添加个推仓库
        maven {
            url = uri("https://mvn.getui.com/nexus/content/repositories/releases/")
        }
        maven {
            url = uri("https://developer.huawei.com/repo/")
        }
        maven {
            url = uri("https://developer.hihonor.com/repo/")
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
