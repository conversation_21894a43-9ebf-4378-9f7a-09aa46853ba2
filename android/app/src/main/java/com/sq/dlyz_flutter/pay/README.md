# 支付策略模式架构

这是一个基于策略模式设计的支付系统，支持多种支付方式的统一管理和扩展。

## 架构设计

### 核心组件

1. **PayStrategy** - 支付策略接口，定义所有支付方式的通用行为
2. **PaymentManager** - 支付管理器，策略模式的上下文类
3. **PaymentFactory** - 支付策略工厂，负责创建支付策略实例
4. **PaymentUtils** - 支付工具类，提供通用工具方法

### 支付策略实现

- **AlipayStrategy** - 支付宝支付策略
- **WechatPayStrategy** - 微信支付策略  
- **HuaweiPayStrategy** - 华为支付策略

### 数据模型

- **PaymentType** - 支付方式枚举
- **PayStatus** - 支付状态枚举
- **PayConfig** - 支付配置
- **PayRequest** - 支付请求参数
- **PayResult** - 支付结果
- **PayQueryResult** - 支付查询结果

### 回调接口

- **PayCallback** - 支付回调接口
- **PayQueryCallback** - 支付查询回调接口
- **SimplePayCallback** - 简化的支付回调抽象类

## 使用方法

### 1. 初始化支付系统

```kotlin
val paymentManager = PaymentManager.getInstance()

// 注册支付策略
val strategies = PaymentFactory.createAllSupportedStrategies()
paymentManager.registerStrategies(*strategies.toTypedArray())

// 配置支付参数
val configs = mapOf(
    PaymentType.ALIPAY to PayConfig(
        appId = "your_alipay_app_id",
        privateKey = "your_private_key",
        isDebug = true
    )
)

// 初始化
paymentManager.initializeAll(context, configs)
```

### 2. 执行支付

```kotlin
val payRequest = PayRequest(
    orderId = "ORDER_123456",
    amount = BigDecimal("99.99"),
    subject = "测试商品",
    body = "商品描述"
)

paymentManager.pay(
    activity = activity,
    paymentType = PaymentType.ALIPAY,
    payRequest = payRequest,
    callback = object : PayCallback {
        override fun onPaySuccess(result: PayResult) {
            // 支付成功处理
        }
        
        override fun onPayFailed(result: PayResult) {
            // 支付失败处理
        }
        
        override fun onPayCancelled(result: PayResult) {
            // 支付取消处理
        }
    }
)
```

### 3. 查询支付结果

```kotlin
paymentManager.queryPayResult(
    paymentType = PaymentType.ALIPAY,
    orderId = "ORDER_123456",
    callback = object : PayQueryCallback {
        override fun onQuerySuccess(result: PayQueryResult) {
            // 查询成功
        }
        
        override fun onQueryFailed(error: String) {
            // 查询失败
        }
    }
)
```

## 扩展新的支付方式

### 1. 创建新的支付策略

```kotlin
class NewPayStrategy : PayStrategy {
    override fun getPaymentName(): String = "新支付方式"
    
    override fun getPaymentType(): PaymentType = PaymentType.NEW_PAY
    
    override fun initialize(context: Context, config: PayConfig) {
        // 初始化逻辑
    }
    
    override fun pay(activity: Activity, payRequest: PayRequest, callback: PayCallback) {
        // 支付逻辑
    }
    
    override fun queryPayResult(orderId: String, callback: PayQueryCallback) {
        // 查询逻辑
    }
    
    override fun isSupported(context: Context): Boolean {
        // 检查是否支持
        return true
    }
    
    override fun release() {
        // 释放资源
    }
}
```

### 2. 在PaymentType中添加新类型

```kotlin
enum class PaymentType(val code: String, val displayName: String) {
    // 现有类型...
    NEW_PAY("newpay", "新支付方式")
}
```

### 3. 在PaymentFactory中注册

```kotlin
object PaymentFactory {
    fun createStrategy(paymentType: PaymentType): PayStrategy? {
        return when (paymentType) {
            // 现有策略...
            PaymentType.NEW_PAY -> NewPayStrategy()
            else -> null
        }
    }
}
```

## 优势特点

1. **可扩展性** - 新增支付方式只需实现PayStrategy接口
2. **可维护性** - 每种支付方式独立实现，互不影响
3. **统一接口** - 所有支付方式使用相同的调用方式
4. **配置灵活** - 支持不同支付方式的独立配置
5. **错误处理** - 统一的错误处理和状态管理
6. **线程安全** - 支持多线程环境下的安全使用

## 注意事项

1. 实际使用时需要集成真实的支付SDK
2. 需要配置正确的支付参数（AppId、密钥等）
3. 支付结果需要服务端验证确保安全性
4. 建议在生产环境中添加更完善的日志和监控
5. 支付敏感信息需要加密存储和传输
