package com.sq.dlyz_flutter;

import android.content.Intent;

import androidx.annotation.NonNull;

import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPlugin;
import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPluginKt;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterActivity {
    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        // 注册DouluoManagerChannel通信通道
        DouluoManagerChannel douluoManagerChannel = new DouluoManagerChannel(this);
        douluoManagerChannel.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
        // 注册FlutterDownloaderPlugin通信通道
        FlutterDownloaderPlugin.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
    }

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        if (getBindingStatus(intent)) {
            // 回调flutter通知绑定成功
            DouluoManagerChannel.getInstance(this).onBindingSuccess();
        }
    }

    private boolean getBindingStatus(Intent intent) {
        try {
            if (intent != null) {
                String bindingStatus = intent.getStringExtra("bindingStatus");
                if ("1".equals(bindingStatus)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
