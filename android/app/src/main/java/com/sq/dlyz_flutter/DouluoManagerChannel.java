package com.sq.dlyz_flutter;

import static androidx.core.content.ContextCompat.getSystemService;

import androidx.annotation.NonNull;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import androidx.core.content.FileProvider;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jarvan.fluwx.handlers.WXAPiHandler;
import com.sq.auth.FastLoginManager;
import com.sq.auth.AuthResultCallback;
import com.sq.dlyz_flutter.connectivity.Connectivity;
import com.sq.dlyz_flutter.connectivity.ConnectivityBroadcastReceiver;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

/**
 * Flutter与原生通信管理类
 */
public class DouluoManagerChannel implements MethodCallHandler {
    private static final String TAG = "DouluoManagerChannel";
    private static final String CHANNEL_NAME = "channel.control/dlyz";
    private static final String EVENT_NAME = "channel.control/dlyz_status";
    private final Context mContext;

    // 单例实例
    private static volatile DouluoManagerChannel instance;
    
    // MethodChannel实例，用于回调Flutter
    private MethodChannel methodChannel;
    private EventChannel eventChannel;

    private Connectivity connectivity;
    private ConnectivityBroadcastReceiver connectivityBroadcastReceiver;

    /**
     * 私有构造函数
     */
    public DouluoManagerChannel(Context context) {
        this.mContext = context;
        ConnectivityManager connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        connectivity = new Connectivity(connectivityManager);
        connectivityBroadcastReceiver = new ConnectivityBroadcastReceiver(mContext, connectivity);
    }

    /**
     * 获取单例实例
     */
    public static DouluoManagerChannel getInstance(Context context) {
        if (instance == null) {
            synchronized (DouluoManagerChannel.class) {
                if (instance == null) {
                    instance = new DouluoManagerChannel(context);
                }
            }
        }
        return instance;
    }

    /**
     * 注册Flutter通信通道
     */
    public void registerWith(BinaryMessenger messenger, Context context) {
        // 创建MethodChannel
        MethodChannel methodChannel = new MethodChannel(messenger, CHANNEL_NAME);
        EventChannel eventChannel = new EventChannel(messenger, EVENT_NAME);

        // 获取实例并设置MethodCallHandler
        DouluoManagerChannel instance = DouluoManagerChannel.getInstance(context);
        instance.methodChannel = methodChannel;
        instance.eventChannel = eventChannel;
        methodChannel.setMethodCallHandler(instance);
        eventChannel.setStreamHandler(connectivityBroadcastReceiver);

        Log.d(TAG, "DouluoManagerChannel registered successfully");
    }

    @Override
    public void onMethodCall(MethodCall call, @NonNull Result result) {
        // 根据方法名处理不同的调用
        switch (call.method) {
            case "getPlatformVersion":
                handleGetPlatformVersion(call, result);
                break;
            case "checkGameInstalled":
                handleCheckGameInstalled(call, result);
                break;
            case "openInstalledGame":
                handleOpenInstalledGame(call, result);
                break;
            case "installApk":
                handleInstallApk(call, result);
                break;
            case "bindingGame":
                handleBindingGame(call, result);
                break;
            case "jumpToMiniProgram":
                jumpToMiniProgram(call, result);
                break;
            case "checkNetworkType":
                checkNetworkType(call, result);
                break;
                
            // 新增: 闪验相关方法
            case "setFastLoginConfig":
                handleSetFastLoginConfig(call, result);
                break;
            case "checkFastLoginEnvironment":
                handleCheckFastLoginEnvironment(result);
                break;
            case "initializeFastLogin":
                handleInitializeFastLogin(result);
                break;
            case "doFastLogin":
                handleDoFastLogin(result);
                break;
            default:
                // 未实现的方法
                result.notImplemented();
                break;
        }
    }

    /**
     * 获取平台版本
     */
    private void handleGetPlatformVersion(MethodCall call, Result result) {
        try {
            String version = android.os.Build.VERSION.RELEASE;
            result.success("Android " + version);
            Log.d(TAG, "Return platform version: " + version);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get platform version: " + e.getMessage());
            result.error("GET_VERSION_FAILED", "获取平台版本失败", e.getMessage());
        }
    }

    /**
     * 检查游戏是否已安装
     */
    private void handleCheckGameInstalled(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            boolean isInstalled = checkAppInstalled(packageName);

            result.success(isInstalled);
            Log.d(TAG, "Check if game installed: " + packageName + " - " + isInstalled);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check game installation: " + e.getMessage());
            result.error("CHECK_INSTALL_FAILED", "检查安装状态失败", e.getMessage());
        }
    }

    /**
     * 打开已安装的游戏
     */
    private void handleOpenInstalledGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }
    
            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void handleInstallApk(MethodCall call, Result result) {
        String filePath = call.argument("filePath");
        if (filePath == null || filePath.isEmpty()) {
            result.error("INVALID_PARAM", "文件路径不能为空", null);
            return;
        }

        File file = new File(filePath);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Uri uri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileProvider", file);
        } else {
            uri = Uri.fromFile(file);
        }

        intent.setDataAndType(uri, "application/vnd.android.package-archive");
        mContext.startActivity(intent);
    }

    private void handleBindingGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                String bindingParams = call.argument("bindingParams");
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                launchIntent.putExtra("bindingParams", bindingParams);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void jumpToMiniProgram(MethodCall call, Result result) {
        if (!checkAppInstalled("com.tencent.mm")) {
            Log.w(TAG, "检测到手机没有安装微信，请安装微信后重试");
            return;
        }
        try {
            String skipType = call.argument("skip_type");
            if (skipType != null) {
                switch (skipType) {
                    case "1":
                        IWXAPI api = WXAPiHandler.INSTANCE.getWxApi();
                        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                        // 填小程序原始id
                        req.userName = call.argument("mini_program_id");
                        // 拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
                        req.path = call.argument("mini_program_path");
                        // 可选打开 开发版，体验版和正式版
                        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
                        if (api != null) {
                            Log.w(TAG, "微信SDK不为空");
                            api.sendReq(req);
                        } else {
                            Log.w(TAG, "微信SDK为null");
                        }
                        break;
                    case "2":
                        String schemeUrl = call.argument("scheme_url");
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.setData(Uri.parse(schemeUrl));
                        mContext.startActivity(intent);
                        break;
                    default:
                        Log.d(TAG, "不支持该类型跳转到微信小程序：" + skipType);
                        break;
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to open mini program: " + e.getMessage());
            result.error("JUMP_MINI_PROGRAM_FAILED", "跳转小程序失败", e.getMessage());
        }

    }

    /**
     * 检查网络状态
     */
    private void checkNetworkType(MethodCall call, Result result) {
        if (connectivity == null) {
            ConnectivityManager connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivity = new Connectivity(connectivityManager);
            connectivityBroadcastReceiver = new ConnectivityBroadcastReceiver(mContext, connectivity);
        }
        List<String> networkTypes = connectivity.getNetworkTypes();
        result.success(networkTypes);
    }


    /**
     * 设置闪验配置
     */
    private void handleSetFastLoginConfig(MethodCall call, Result result) {
        try {
            Map<String, Object> config = (Map<String, Object>) call.arguments;
            Log.d(TAG, "设置闪验配置: " + config);
            
            Object accessKeyObj = config.get("access_key");
            Object sceneCodeObj = config.get("scene_code");
            Object appKeyObj = config.get("app_key");
            Object userAgreementUrlObj = config.get("userAgreementUrl");
            Object privacyPolicyUrlObj = config.get("privacyPolicyUrl");
            
            String accessKey = accessKeyObj != null ? accessKeyObj.toString() : null;
            String sceneCode = sceneCodeObj != null ? sceneCodeObj.toString() : null;
            String appKey = appKeyObj != null ? appKeyObj.toString() : null;
            String userAgreementUrl = userAgreementUrlObj != null ? userAgreementUrlObj.toString() : null;
            String privacyPolicyUrl = privacyPolicyUrlObj != null ? privacyPolicyUrlObj.toString() : null;
            
            FastLoginManager fastLoginManager = FastLoginManager.getInstance(mContext);
            
            // 设置协议地址
            if (userAgreementUrl != null && privacyPolicyUrl != null) {
                fastLoginManager.setAgreementUrls(userAgreementUrl, privacyPolicyUrl);
                Log.d(TAG, "设置协议地址 - 用户协议: " + userAgreementUrl);
                Log.d(TAG, "设置协议地址 - 隐私政策: " + privacyPolicyUrl);
            }
            
            // 先设置解密密钥
            if (appKey != null && !appKey.isEmpty()) {
                fastLoginManager.setAppKey(appKey);
                Log.d(TAG, "Setting app key for decryption");
            }
            
            // 再设置accessKey（会使用appKey进行解密）
            if (accessKey != null && !accessKey.isEmpty()) {
                Log.d(TAG, "Setting access key: " + accessKey);
                Log.d(TAG, "Setting scene code: " + sceneCode);
                
                fastLoginManager.setAccessKey(accessKey);
                
                result.success(true);
            } else {
                Log.w(TAG, "No access key found in config");
                result.success(false);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to set fast login config: " + e.getMessage());
            result.error("SET_CONFIG_FAILED", "设置闪验配置失败", e.getMessage());
        }
    }
    
    /**
     * 检查闪验环境
     */
    private void handleCheckFastLoginEnvironment(Result result) {
        try {
            Log.d(TAG, "Checking fast login environment");
            FastLoginManager.getInstance(mContext).getFastEnv(isSupported -> {
                Log.d(TAG, "Fast login environment check result: " + isSupported);
                result.success(isSupported);
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to check fast login environment: " + e.getMessage());
            result.error("CHECK_ENV_FAILED", "检查闪验环境失败", e.getMessage());
        }
    }
    
    /**
     * 初始化闪验SDK
     */
    private void handleInitializeFastLogin(Result result) {
        try {
            Log.d(TAG, "Initializing fast login SDK");
            FastLoginManager.getInstance(mContext).initFastAccessKey(new AuthResultCallback() {
                @Override
                public void onSuccess() {
                    Log.d(TAG, "Fast login SDK initialized successfully");
                    result.success(true);
                }
                
                @Override
                public void onFailure(int code, String msg) {
                    Log.e(TAG, "Failed to initialize fast login SDK: " + code + " - " + msg);
                    result.error("INIT_FAILED", msg, String.valueOf(code));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login initialization: " + e.getMessage());
            result.error("INIT_EXCEPTION", "初始化异常", e.getMessage());
        }
    }
    
    /**
     * 执行闪验登录
     */
    private void handleDoFastLogin(Result result) {
        try {
            Log.d(TAG, "Starting fast login process");
            final boolean[] responded = new boolean[]{false};
            FastLoginManager.getInstance(mContext).doFastVerifyLogin(new FastLoginManager.FastLoginListener() {
                @Override
                public void onFastLoginSuccess(String token) {
                    Log.d(TAG, "Fast login successful, token: " + token);
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("success", true);
                    resultMap.put("token", token);
                    if (!responded[0]) {
                        responded[0] = true;
                        result.success(resultMap);
                    }
                }
                
                @Override
                public void onFastLoginFail(Bundle bundle) {
                    String code = bundle.getString("CODE", "UNKNOWN");
                    String message = bundle.getString("MESSAGE", "Unknown error");
                    Log.e(TAG, "Fast login failed: " + code + " - " + message);
                    if (!responded[0]) {
                        responded[0] = true;
                        result.error(code, message, null);
                    }
                }
                
                @Override
                public void onFastRelease() {
                    Log.d(TAG, "Fast login UI released");
                    // 若未返回结果，则视为取消，保证Flutter端能够继续流程
                    if (!responded[0]) {
                        responded[0] = true;
                        result.error("CANCEL", "User canceled", null);
                    }
                }
                
                @Override
                public void onVerifyAccount(boolean needOpen) {
                    Log.d(TAG, "Verify account callback: needOpen=" + needOpen);
                    // 账号验证回调可以通过其他方式处理，或者合并到结果中
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login: " + e.getMessage());
            result.error("FAST_LOGIN_EXCEPTION", "闪验登录异常", e.getMessage());
        }
    }
    
    /**
     * 闪验UI关闭按钮点击回调
     */
    public void onFastLoginCloseClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "close_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login close event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send close event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 闪验UI返回按钮点击回调
     */
    public void onFastLoginBackClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "back_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login back event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send back event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 忘记密码点击回调
     */
    public void onFastLoginForgetPasswordClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "forget_password_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login forget password event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send forget password event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 微信登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginWeChatClicked() {
        try {
            // 关闭闪验授权页，避免覆盖Flutter界面
            FastLoginManager.getInstance(mContext).quitLoginPage();
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "wechat_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login wechat event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send wechat event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 账号密码登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginAccountLoginClicked() {
        try {
            // 关闭闪验授权页，避免覆盖Flutter界面
            FastLoginManager.getInstance(mContext).quitLoginPage();
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "account_login_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login account login event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send account login event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 其他手机号登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginOtherPhoneClicked() {
        try {
            // 关闭闪验授权页，避免覆盖Flutter界面
            FastLoginManager.getInstance(mContext).quitLoginPage();
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "other_phone_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login other phone event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send other phone event to Flutter: " + e.getMessage());
        }
    }

    public void onBindingSuccess() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                methodChannel.invokeMethod("onBindingSuccess", arguments);
                Log.d(TAG, "Binding success event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send binding success event to Flutter: " + e.getMessage());
        }
    }

    private boolean checkAppInstalled(String packageName) {
        if (packageName == null || packageName.isEmpty()) {
            return false;
        }

        PackageManager packageManager = mContext.getPackageManager();
        try {
            // 尝试获取包信息，如果不存在会抛出NameNotFoundException
            packageManager.getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            // 应用未安装
            return false;
        }
    }
}
