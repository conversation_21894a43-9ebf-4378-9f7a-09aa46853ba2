package com.sq.dlyz_flutter.pay

/**
 * 支付方式类型枚举
 */
enum class PaymentType(val code: String, val displayName: String) {
    ALIPAY("alipay", "支付宝"),
    UNKNOWN("unknown", "未知支付方式");

    companion object {
        /**
         * 根据code获取支付类型
         */
        fun fromCode(code: String): PaymentType {
            return values().find { it.code == code } ?: UNKNOWN
        }
    }
}

/**
 * 支付状态枚举
 */
enum class PayStatus(val code: Int, val message: String) {
    SUCCESS(0, "支付成功"),
    FAILED(-1, "支付失败"),
    CANCELLED(-2, "用户取消"),
    PENDING(1, "支付中"),
    TIMEOUT(-3, "支付超时"),
    NETWORK_ERROR(-4, "网络错误"),
    INVALID_PARAMS(-5, "参数错误"),
    NOT_INSTALLED(-6, "应用未安装"),
    UNKNOWN(-999, "未知错误");

    companion object {
        fun fromCode(code: Int): PayStatus {
            return values().find { it.code == code } ?: UNKNOWN
        }
    }
}
