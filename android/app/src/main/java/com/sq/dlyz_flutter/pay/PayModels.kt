package com.sq.dlyz_flutter.pay

import java.math.BigDecimal

/**
 * 支付配置
 */
data class PayConfig(
    val appId: String = "",
    val merchantId: String = "",
    val privateKey: String = "",
    val publicKey: String = "",
    val notifyUrl: String = "",
    val isDebug: Boolean = false,
    val extraParams: Map<String, String> = emptyMap()
)

/**
 * 支付请求参数
 */
data class PayRequest(
    val orderId: String,                    // 订单ID
    val amount: BigDecimal,                 // 支付金额
    val currency: String = "CNY",           // 货币类型
    val subject: String,                    // 商品标题
    val body: String = "",                  // 商品描述
    val notifyUrl: String = "",             // 异步通知地址
    val returnUrl: String = "",             // 同步返回地址
    val timeoutExpress: String = "30m",     // 超时时间
    val passbackParams: String = "",        // 回传参数
    val extraParams: Map<String, Any> = emptyMap() // 额外参数
)

/**
 * 支付结果
 */
data class PayResult(
    val status: PayStatus,                  // 支付状态
    val orderId: String = "",               // 订单ID
    val transactionId: String = "",         // 第三方交易号
    val amount: BigDecimal? = null,         // 支付金额
    val message: String = "",               // 结果描述
    val rawData: String = "",               // 原始数据
    val extraData: Map<String, Any> = emptyMap() // 额外数据
)

/**
 * 支付查询结果
 */
data class PayQueryResult(
    val orderId: String,                    // 订单ID
    val transactionId: String = "",         // 第三方交易号
    val status: PayStatus,                  // 支付状态
    val amount: BigDecimal? = null,         // 支付金额
    val payTime: Long = 0L,                 // 支付时间戳
    val message: String = "",               // 结果描述
    val extraData: Map<String, Any> = emptyMap() // 额外数据
)
