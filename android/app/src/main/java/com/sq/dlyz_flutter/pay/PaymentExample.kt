package com.sq.dlyz_flutter.pay

import android.app.Activity
import android.content.Context
import android.util.Log
import java.math.BigDecimal

/**
 * 支付使用示例
 * 演示如何使用支付策略模式进行支付
 */
class PaymentExample {
    
    companion object {
        private const val TAG = "PaymentExample"
    }
    
    /**
     * 初始化支付
     */
    fun initializePaymentSystem(context: Context) {
        val paymentManager = PaymentManager.getInstance()
        
        // 注册所有支付策略
        val strategies = PaymentFactory.createAllSupportedStrategies()
        paymentManager.registerStrategies(*strategies.toTypedArray())
        
        // 配置各支付方式的参数
        val configs = mapOf(
            PaymentType.ALIPAY to PaymentUtils.createDefaultPayConfig(
                appId = "your_alipay_app_id",
                privateKey = "your_alipay_private_key",
                isDebug = true
            )
        )
        
        // 初始化所有支付策略
        paymentManager.initializeAll(context, configs)
        
        Log.d(TAG, "支付系统初始化完成")
    }
    
    /**
     * 执行支付宝支付
     */
    fun payWithAlipay(activity: Activity) {
        val payRequest = PayRequest(
            orderId = PaymentUtils.generateOrderId("ALIPAY"),
            amount = BigDecimal("99.99"),
            subject = "测试商品",
            body = "这是一个测试商品的描述",
            notifyUrl = "https://your-server.com/notify"
        )
        
        // 验证支付参数
        val validation = PaymentUtils.validatePayRequest(payRequest)
        if (!validation.isValid) {
            Log.e(TAG, "支付参数验证失败: ${validation.errors}")
            return
        }
        
        val paymentManager = PaymentManager.getInstance()
        paymentManager.pay(
            activity = activity,
            paymentType = PaymentType.ALIPAY,
            payRequest = payRequest,
            callback = object : PayCallback {
                override fun onPaySuccess(result: PayResult) {
                    Log.d(TAG, "支付宝支付成功: ${result.orderId}")
                    // 处理支付成功逻辑
                }
                
                override fun onPayFailed(result: PayResult) {
                    Log.e(TAG, "支付宝支付失败: ${result.message}")
                    // 处理支付失败逻辑
                }
                
                override fun onPayCancelled(result: PayResult) {
                    Log.d(TAG, "支付宝支付取消: ${result.orderId}")
                    // 处理支付取消逻辑
                }
                
                override fun onPayPending(result: PayResult) {
                    Log.d(TAG, "支付宝支付处理中: ${result.orderId}")
                    // 处理支付处理中逻辑
                }
            }
        )
    }
    
    /**
     * 获取支持的支付方式
     */
    fun getSupportedPaymentMethods(context: Context): List<PaymentType> {
        val paymentManager = PaymentManager.getInstance()
        return paymentManager.getSupportedPaymentTypes(context)
    }
    
    /**
     * 创建简单的支付回调
     */
    private fun createSimplePayCallback(paymentName: String): PayCallback {
        return object : SimplePayCallback() {
            override fun onPaySuccess(result: PayResult) {
                Log.d(TAG, "$paymentName 支付成功: ${result.orderId}")
            }
            
            override fun onPayFailed(result: PayResult) {
                Log.e(TAG, "$paymentName 支付失败: ${result.message}")
            }
            
            override fun onPayCancelled(result: PayResult) {
                Log.d(TAG, "$paymentName 支付取消: ${result.orderId}")
            }
        }
    }
    
    /**
     * 释放支付系统资源
     */
    fun releasePaymentSystem() {
        val paymentManager = PaymentManager.getInstance()
        paymentManager.releaseAll()
        Log.d(TAG, "支付系统资源已释放")
    }
}
