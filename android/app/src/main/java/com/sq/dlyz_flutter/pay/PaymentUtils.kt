package com.sq.dlyz_flutter.pay

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import java.math.BigDecimal
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*

/**
 * 支付工具类
 * 提供支付相关的通用工具方法
 */
object PaymentUtils {
    
    private const val TAG = "PaymentUtils"
    
    /**
     * 生成订单号
     */
    fun generateOrderId(prefix: String = "ORDER"): String {
        val timestamp = System.currentTimeMillis()
        val random = (Math.random() * 10000).toInt()
        return "${prefix}_${timestamp}_${random}"
    }
    
    /**
     * 格式化金额（保留两位小数）
     */
    fun formatAmount(amount: BigDecimal): String {
        return String.format("%.2f", amount)
    }
    
    /**
     * 验证金额是否有效
     */
    fun isValidAmount(amount: BigDecimal?): Boolean {
        return amount != null && amount > BigDecimal.ZERO
    }
    
    /**
     * 验证订单ID是否有效
     */
    fun isValidOrderId(orderId: String?): Boolean {
        return !orderId.isNullOrBlank() && orderId.length <= 64
    }
    
    /**
     * 生成时间戳字符串
     */
    fun generateTimestamp(): String {
        return (System.currentTimeMillis() / 1000).toString()
    }
    
    /**
     * 生成随机字符串
     */
    fun generateRandomString(length: Int = 32): String {
        val chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..length).map { chars.random() }.joinToString("")
    }
    
    /**
     * MD5加密
     */
    fun md5(input: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "MD5加密失败", e)
            ""
        }
    }
    
    /**
     * 检查应用是否安装
     */
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 获取应用版本号
     */
    fun getAppVersionCode(context: Context, packageName: String): Int {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
            packageInfo.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            -1
        }
    }
    
    /**
     * 格式化时间
     */
    fun formatTime(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        return try {
            val sdf = SimpleDateFormat(pattern, Locale.getDefault())
            sdf.format(Date(timestamp))
        } catch (e: Exception) {
            Log.e(TAG, "时间格式化失败", e)
            ""
        }
    }
    
    /**
     * 验证支付请求参数
     */
    fun validatePayRequest(payRequest: PayRequest): PayValidationResult {
        val errors = mutableListOf<String>()
        
        // 验证订单ID
        if (!isValidOrderId(payRequest.orderId)) {
            errors.add("订单ID无效")
        }
        
        // 验证金额
        if (!isValidAmount(payRequest.amount)) {
            errors.add("支付金额无效")
        }
        
        // 验证商品标题
        if (payRequest.subject.isBlank()) {
            errors.add("商品标题不能为空")
        }
        
        // 验证货币类型
        if (payRequest.currency.isBlank()) {
            errors.add("货币类型不能为空")
        }
        
        return PayValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
    
    /**
     * 创建默认支付配置
     */
    fun createDefaultPayConfig(
        appId: String,
        merchantId: String = "",
        privateKey: String = "",
        publicKey: String = "",
        isDebug: Boolean = false
    ): PayConfig {
        return PayConfig(
            appId = appId,
            merchantId = merchantId,
            privateKey = privateKey,
            publicKey = publicKey,
            isDebug = isDebug
        )
    }
    
    /**
     * 解析支付结果状态码
     */
    fun parsePayStatusFromCode(code: String): PayStatus {
        return when (code) {
            "0", "9000", "SUCCESS" -> PayStatus.SUCCESS
            "-1", "FAILED" -> PayStatus.FAILED
            "-2", "6001", "CANCELLED" -> PayStatus.CANCELLED
            "1", "8000", "PENDING" -> PayStatus.PENDING
            "-3", "TIMEOUT" -> PayStatus.TIMEOUT
            "-4", "NETWORK_ERROR" -> PayStatus.NETWORK_ERROR
            "-5", "INVALID_PARAMS" -> PayStatus.INVALID_PARAMS
            "-6", "NOT_INSTALLED" -> PayStatus.NOT_INSTALLED
            else -> PayStatus.UNKNOWN
        }
    }
}

/**
 * 支付参数验证结果
 */
data class PayValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
