group 'io.flutter.plugins.sharedpreferences'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '2.1.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'io.flutter.plugins.sharedpreferences'
    compileSdk = flutter.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }
    dependencies {
        implementation 'androidx.datastore:datastore:1.1.3'
        implementation 'androidx.datastore:datastore-preferences:1.1.3'
        implementation 'androidx.preference:preference:1.2.1'
        testImplementation 'junit:junit:4.13.2'
        testImplementation 'androidx.test:core-ktx:1.5.0'
        testImplementation 'androidx.test.ext:junit-ktx:1.2.1'
        testImplementation 'org.robolectric:robolectric:4.12.1'
        testImplementation 'org.mockito:mockito-inline:5.2.0'
        testImplementation 'io.mockk:mockk:1.13.16'
    }


    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
